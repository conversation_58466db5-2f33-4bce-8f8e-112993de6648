import { TestBed } from '@angular/core/testing';
import { Component } from '@angular/core';
import { of, Observable, throwError } from 'rxjs';
import { RolesAwareComponent } from './roles-aware.component';
import { UserProfileService } from '@shared/services/user-profile.service';
import { UserRole } from '@shared/models/user-role.model';
import { UserProfile } from '@shared/models/user-profile.model';

// Create a concrete test component that extends the abstract RolesAwareComponent
@Component({
	template: '<div>Test Component</div>',
	standalone: true,
})
class TestRolesAwareComponent extends RolesAwareComponent {}

describe('RolesAwareComponent', () => {
	let component: TestRolesAwareComponent;
	let mockUserProfileService: jasmine.SpyObj<UserProfileService>;

	// Helper function to create mock UserProfile
	const createMockUserProfile = (overrides: Partial<UserProfile> = {}): UserProfile => ({
		userId: 'test-user-id',
		email: '<EMAIL>',
		firstName: 'Test',
		lastName: 'User',
		primaryOrgId: 'primary-org',
		primaryOrgName: 'Primary Org',
		orgId: 'org-id',
		orgName: 'Org Name',
		orgType: 'SHP',
		userType: '1',
		menuList: [],
		permissionList: [
			{
				name: 'SLI Management',
				module: 'sli',
				code: ['create', 'update', 'query'],
			},
		],
		orgList: [],
		...overrides,
	});

	beforeEach(async () => {
		// Create spy for UserProfileService
		mockUserProfileService = jasmine.createSpyObj('UserProfileService', ['hasSomeRole', 'hasPermission', 'isSuperUser'], {
			currentUser$: of(createMockUserProfile()),
		});

		await TestBed.configureTestingModule({
			imports: [TestRolesAwareComponent],
			providers: [{ provide: UserProfileService, useValue: mockUserProfileService }],
		}).compileComponents();

		const fixture = TestBed.createComponent(TestRolesAwareComponent);
		component = fixture.componentInstance;
	});

	describe('Component Creation', () => {
		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should extend DestroyRefComponent', () => {
			expect(component['destroyRef']).toBeDefined();
		});

		it('should have userRoles property set to UserRole enum', () => {
			expect(component.userRoles).toBe(UserRole);
		});

		it('should inject UserProfileService', () => {
			expect(component['profileService']).toBe(mockUserProfileService);
		});
	});

	describe('hasSomeRole method', () => {
		it('should return Observable<boolean> from profileService.hasSomeRole when service exists', (done: DoneFn) => {
			// Arrange
			const testRoles = [UserRole.SHIPPER, UserRole.FORWARDER];
			mockUserProfileService.hasSomeRole.and.returnValue(of(true));

			// Act
			component.hasSomeRole(testRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(mockUserProfileService.hasSomeRole).toHaveBeenCalledWith(testRoles);
				done();
			});
		});

		it('should return Observable<false> when profileService is null', (done: DoneFn) => {
			// Arrange
			component['profileService'] = null as any;
			const testRoles = [UserRole.SHIPPER];

			// Act
			component.hasSomeRole(testRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				done();
			});
		});

		it('should handle empty roles array', (done: DoneFn) => {
			// Arrange
			mockUserProfileService.hasSomeRole.and.returnValue(of(false));

			// Act
			component.hasSomeRole([]).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(mockUserProfileService.hasSomeRole).toHaveBeenCalledWith([]);
				done();
			});
		});

		it('should handle service errors', (done: DoneFn) => {
			// Arrange
			const testRoles = [UserRole.SHIPPER];
			const errorMessage = 'Service error';
			mockUserProfileService.hasSomeRole.and.returnValue(throwError(() => new Error(errorMessage)));

			// Act
			component.hasSomeRole(testRoles).subscribe({
				next: () => fail('Should have thrown an error'),
				error: (error) => {
					// Assert
					expect(error.message).toBe(errorMessage);
					done();
				},
			});
		});
	});

	describe('hasPermission method', () => {
		it('should return Observable<boolean> from profileService.hasPermission when service exists', (done: DoneFn) => {
			// Arrange
			const permission = 'create';
			const module = 'sli';
			mockUserProfileService.hasPermission.and.returnValue(of(true));

			// Act
			component.hasPermission(permission, module).subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(mockUserProfileService.hasPermission).toHaveBeenCalledWith(permission, module);
				done();
			});
		});

		it('should return Observable<false> when profileService is null', (done: DoneFn) => {
			// Arrange
			component['profileService'] = null as any;

			// Act
			component.hasPermission('create', 'sli').subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				done();
			});
		});

		it('should handle different permission and module combinations', (done: DoneFn) => {
			// Arrange
			const testCases = [
				{ permission: 'update', module: 'hawb', expected: false },
				{ permission: 'delete', module: 'mawb', expected: true },
				{ permission: 'query', module: 'sli', expected: true },
			];

			let completedTests = 0;

			testCases.forEach((testCase) => {
				mockUserProfileService.hasPermission.and.returnValue(of(testCase.expected));

				component.hasPermission(testCase.permission, testCase.module).subscribe((result) => {
					expect(result).toBe(testCase.expected);
					expect(mockUserProfileService.hasPermission).toHaveBeenCalledWith(testCase.permission, testCase.module);

					completedTests++;
					if (completedTests === testCases.length) {
						done();
					}
				});
			});
		});

		it('should handle service errors', (done: DoneFn) => {
			// Arrange
			const errorMessage = 'Permission check failed';
			mockUserProfileService.hasPermission.and.returnValue(throwError(() => new Error(errorMessage)));

			// Act
			component.hasPermission('create', 'sli').subscribe({
				next: () => fail('Should have thrown an error'),
				error: (error) => {
					// Assert
					expect(error.message).toBe(errorMessage);
					done();
				},
			});
		});
	});

	describe('getCurrentUser method', () => {
		it('should return currentUser$ Observable from profileService', (done: DoneFn) => {
			// Arrange
			const mockUser = createMockUserProfile();

			// Act
			component.getCurrentUser().subscribe((user) => {
				// Assert
				expect(user).toEqual(mockUser);
				done();
			});
		});

		it('should return null when no user is available', (done: DoneFn) => {
			// Arrange
			Object.defineProperty(mockUserProfileService, 'currentUser$', {
				value: of(null),
			});

			// Act
			component.getCurrentUser().subscribe((user) => {
				// Assert
				expect(user).toBeNull();
				done();
			});
		});

		it('should handle service errors', (done: DoneFn) => {
			// Arrange
			const errorMessage = 'User fetch failed';
			Object.defineProperty(mockUserProfileService, 'currentUser$', {
				value: throwError(() => new Error(errorMessage)),
			});

			// Act
			component.getCurrentUser().subscribe({
				next: () => fail('Should have thrown an error'),
				error: (error) => {
					// Assert
					expect(error.message).toBe(errorMessage);
					done();
				},
			});
		});
	});

	describe('isSuperUser method', () => {
		it('should return Observable<boolean> from profileService.isSuperUser', (done: DoneFn) => {
			// Arrange
			mockUserProfileService.isSuperUser.and.returnValue(true);

			// Act
			component.isSuperUser().subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(mockUserProfileService.isSuperUser).toHaveBeenCalled();
				done();
			});
		});

		it('should return false when user is not super user', (done: DoneFn) => {
			// Arrange
			mockUserProfileService.isSuperUser.and.returnValue(false);

			// Act
			component.isSuperUser().subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(mockUserProfileService.isSuperUser).toHaveBeenCalled();
				done();
			});
		});

		it('should handle null profileService', (done: DoneFn) => {
			// Arrange
			component['profileService'] = null as any;

			// Act & Assert
			expect(() => {
				component.isSuperUser().subscribe();
			}).toThrow();
			done();
		});
	});

	describe('Integration Tests', () => {
		it('should work with real UserRole enum values', (done: DoneFn) => {
			// Arrange
			const realRoles = [UserRole.SHIPPER, UserRole.FORWARDER, UserRole.CARRIER];
			mockUserProfileService.hasSomeRole.and.returnValue(of(true));

			// Act
			component.hasSomeRole(realRoles).subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(mockUserProfileService.hasSomeRole).toHaveBeenCalledWith(['SHP', 'FFW', 'AIR']);
				done();
			});
		});

		it('should handle multiple concurrent role checks', (done: DoneFn) => {
			// Arrange
			const roleChecks = [
				{ roles: [UserRole.SHIPPER], expected: true },
				{ roles: [UserRole.FORWARDER], expected: false },
				{ roles: [UserRole.CARRIER], expected: true },
			];

			let completedChecks = 0;

			roleChecks.forEach((check, index) => {
				mockUserProfileService.hasSomeRole.and.returnValue(of(check.expected));

				component.hasSomeRole(check.roles).subscribe((result) => {
					expect(result).toBe(check.expected);
					completedChecks++;

					if (completedChecks === roleChecks.length) {
						done();
					}
				});
			});
		});

		it('should handle multiple concurrent permission checks', (done: DoneFn) => {
			// Arrange
			const permissionChecks = [
				{ permission: 'create', module: 'sli', expected: true },
				{ permission: 'update', module: 'hawb', expected: false },
				{ permission: 'delete', module: 'mawb', expected: true },
			];

			let completedChecks = 0;

			permissionChecks.forEach((check) => {
				mockUserProfileService.hasPermission.and.returnValue(of(check.expected));

				component.hasPermission(check.permission, check.module).subscribe((result) => {
					expect(result).toBe(check.expected);
					completedChecks++;

					if (completedChecks === permissionChecks.length) {
						done();
					}
				});
			});
		});

		it('should work with complex user profile data', (done: DoneFn) => {
			// Arrange
			const complexUser = createMockUserProfile({
				orgType: UserRole.FORWARDER,
				userType: UserRole.SUPER_USER,
				permissionList: [
					{
						name: 'SLI Management',
						module: 'sli',
						code: ['create', 'update', 'query', 'delete'],
					},
					{
						name: 'HAWB Management',
						module: 'hawb',
						code: ['query', 'share'],
					},
				],
			});

			Object.defineProperty(mockUserProfileService, 'currentUser$', {
				value: of(complexUser),
			});

			// Act
			component.getCurrentUser().subscribe((user) => {
				// Assert
				expect(user).toEqual(complexUser);
				expect(user?.orgType).toBe(UserRole.FORWARDER);
				expect(user?.userType).toBe(UserRole.SUPER_USER);
				expect(user?.permissionList?.length).toBe(2);
				done();
			});
		});
	});

	describe('Edge Cases and Error Handling', () => {
		it('should handle undefined roles array', (done: DoneFn) => {
			// Arrange
			mockUserProfileService.hasSomeRole.and.returnValue(of(false));

			// Act
			component.hasSomeRole(undefined as any).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(mockUserProfileService.hasSomeRole).toHaveBeenCalledWith(jasmine.any(Object));
				done();
			});
		});

		it('should handle empty string permission and module', (done: DoneFn) => {
			// Arrange
			mockUserProfileService.hasPermission.and.returnValue(of(false));

			// Act
			component.hasPermission('', '').subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				expect(mockUserProfileService.hasPermission).toHaveBeenCalledWith('', '');
				done();
			});
		});

		it('should handle service method throwing synchronous errors', () => {
			// Arrange
			mockUserProfileService.isSuperUser.and.throwError('Synchronous error');

			// Act & Assert
			expect(() => {
				component.isSuperUser();
			}).toThrow('Synchronous error');
		});

		it('should handle profileService being undefined', (done: DoneFn) => {
			// Arrange
			component['profileService'] = undefined as any;

			// Act
			component.hasSomeRole([UserRole.SHIPPER]).subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				done();
			});
		});

		it('should handle profileService hasPermission being undefined', (done: DoneFn) => {
			// Arrange
			component['profileService'] = undefined as any;

			// Act
			component.hasPermission('create', 'sli').subscribe((result) => {
				// Assert
				expect(result).toBe(false);
				done();
			});
		});
	});

	describe('UserRole Enum Integration', () => {
		it('should have access to all UserRole enum values', () => {
			expect(component.userRoles.SHIPPER).toBe('SHP');
			expect(component.userRoles.FORWARDER).toBe('FFW');
			expect(component.userRoles.CARRIER).toBe('AIR');
			expect(component.userRoles.SUPER_USER).toBe('3');
			expect(component.userRoles.CONSIGNEE).toBe('CNE');
			expect(component.userRoles.CUSTOM).toBe('CTM');
			expect(component.userRoles.AGENT).toBe('GHA');
			expect(component.userRoles.SECURITY).toBe('APT');
		});

		it('should use UserRole enum values in role checks', (done: DoneFn) => {
			// Arrange
			const roles = [component.userRoles.SHIPPER, component.userRoles.FORWARDER];
			mockUserProfileService.hasSomeRole.and.returnValue(of(true));

			// Act
			component.hasSomeRole(roles).subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				expect(mockUserProfileService.hasSomeRole).toHaveBeenCalledWith(['SHP', 'FFW']);
				done();
			});
		});
	});

	describe('Observable Chain Handling', () => {
		it('should properly handle Observable chains for role checks', (done: DoneFn) => {
			// Arrange
			mockUserProfileService.hasSomeRole.and.returnValue(
				of(false).pipe(
					// Simulate some async processing
					(source) =>
						new Observable((subscriber) => {
							setTimeout(() => {
								subscriber.next(true);
								subscriber.complete();
							}, 10);
						})
				)
			);

			// Act
			component.hasSomeRole([UserRole.SHIPPER]).subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				done();
			});
		});

		it('should handle Observable chains for permission checks', (done: DoneFn) => {
			// Arrange
			mockUserProfileService.hasPermission.and.returnValue(
				of(false).pipe(
					// Simulate some async processing
					(source) =>
						new Observable((subscriber) => {
							setTimeout(() => {
								subscriber.next(true);
								subscriber.complete();
							}, 10);
						})
				)
			);

			// Act
			component.hasPermission('create', 'sli').subscribe((result) => {
				// Assert
				expect(result).toBe(true);
				done();
			});
		});
	});
});
